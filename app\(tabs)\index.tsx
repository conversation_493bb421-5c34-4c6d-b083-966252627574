import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  RefreshControl,
} from 'react-native';
import { PropertyCard } from '@/components/PropertyCard';
import { SearchHeader } from '@/components/SearchHeader';
import { router } from 'expo-router';

interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  imageUrl: string;
  type: string;
  isFavorite: boolean;
  description?: string;
  yearBuilt?: number;
  lotSize?: number;
  garage?: number;
  features?: string[];
  images?: string[];
  agent?: {
    name: string;
    phone: string;
    email: string;
    photo: string;
  };
}

const mockProperties: Property[] = [
  {
    id: '1',
    title: 'Modern Lekki Apartment',
    price: 85000000,
    location: 'Lekki Phase 1, Lagos',
    bedrooms: 3,
    bathrooms: 3,
    area: 120,
    imageUrl: 'https://images.pexels.com/photos/1438832/pexels-photo-1438832.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Apartment',
    isFavorite: false,
    description: 'Stunning modern apartment in the heart of Lekki Phase 1. This beautifully designed space features floor-to-ceiling windows, contemporary finishes, and premium amenities. Perfect for professionals seeking luxury and convenience in Lagos.',
    yearBuilt: 2020,
    lotSize: 0,
    garage: 2,
    features: ['Marble Floors', 'Fitted Kitchen', 'Generator', 'Swimming Pool', 'Gym Access', 'Security'],
    images: [
      'https://images.pexels.com/photos/1438832/pexels-photo-1438832.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Adebayo Ogundimu',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
  {
    id: '2',
    title: 'Luxury Ikoyi Mansion',
    price: 250000000,
    location: 'Ikoyi, Lagos',
    bedrooms: 5,
    bathrooms: 6,
    area: 450,
    imageUrl: 'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Mansion',
    isFavorite: true,
    description: 'Exquisite luxury mansion in prestigious Ikoyi. This magnificent family home boasts premium finishes, spacious rooms, and beautifully landscaped gardens. Perfect for affluent families seeking elegance and comfort.',
    yearBuilt: 2019,
    lotSize: 1200,
    garage: 4,
    features: ['Master Suite', 'Home Cinema', 'Wine Cellar', 'Swimming Pool', 'Manicured Garden', 'Staff Quarters'],
    images: [
      'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Funmi Adebisi',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
  {
    id: '3',
    title: 'Cozy Surulere Duplex',
    price: 45000000,
    location: 'Surulere, Lagos',
    bedrooms: 4,
    bathrooms: 3,
    area: 200,
    imageUrl: 'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Duplex',
    isFavorite: false,
    description: 'Charming duplex in vibrant Surulere. This family-friendly home features spacious rooms, modern amenities, and a lovely compound. Perfect for growing families or investors seeking value.',
    yearBuilt: 2018,
    lotSize: 600,
    garage: 2,
    features: ['Spacious Compound', 'Modern Kitchen', 'Family Lounge', 'Boys Quarter', 'Parking Space', 'Security'],
    images: [
      'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1438832/pexels-photo-1438832.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Chinedu Okoro',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
  {
    id: '4',
    title: 'Victoria Island Penthouse',
    price: 180000000,
    location: 'Victoria Island, Lagos',
    bedrooms: 4,
    bathrooms: 4,
    area: 300,
    imageUrl: 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Penthouse',
    isFavorite: true,
    description: 'Spectacular penthouse with panoramic views of Lagos lagoon and Atlantic Ocean. This luxury residence features floor-to-ceiling windows, premium finishes, and a private terrace. The epitome of Lagos luxury living.',
    yearBuilt: 2021,
    lotSize: 0,
    garage: 3,
    features: ['Ocean Views', 'Private Terrace', 'Concierge Service', 'Smart Home', 'Infinity Pool', 'Valet Parking'],
    images: [
      'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1438832/pexels-photo-1438832.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Kemi Adesanya',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
  {
    id: '5',
    title: 'Elegant Ikeja Bungalow',
    price: 35000000,
    location: 'GRA Ikeja, Lagos',
    bedrooms: 3,
    bathrooms: 2,
    area: 150,
    imageUrl: 'https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Bungalow',
    isFavorite: false,
    description: 'Beautiful bungalow in the prestigious GRA Ikeja. This well-maintained home features classic architecture, modern amenities, and a spacious compound. Perfect for those who appreciate comfort and convenience.',
    yearBuilt: 2017,
    lotSize: 800,
    garage: 2,
    features: ['Spacious Rooms', 'Modern Kitchen', 'Large Compound', 'Boys Quarter', 'Parking Space', 'Generator House'],
    images: [
      'https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Tunde Bakare',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
];

export default function HomeScreen() {
  const [properties, setProperties] = useState<Property[]>(mockProperties);
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const toggleFavorite = (propertyId: string) => {
    setProperties(prev =>
      prev.map(property =>
        property.id === propertyId
          ? { ...property, isFavorite: !property.isFavorite }
          : property
      )
    );
  };

  const handlePropertyPress = (property: Property) => {
    router.push({
      pathname: '/property/[id]',
      params: { 
        id: property.id,
        propertyData: JSON.stringify(property)
      }
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <SearchHeader />
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <View style={styles.header}>
          <Text style={styles.title}>Featured Properties</Text>
          <Text style={styles.subtitle}>
            Discover your perfect home from our curated selection
          </Text>
        </View>
        
        <View style={styles.propertiesContainer}>
          {properties.map(property => (
            <PropertyCard
              key={property.id}
              property={property}
              onToggleFavorite={toggleFavorite}
              onPress={() => handlePropertyPress(property)}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 24,
  },
  propertiesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
});