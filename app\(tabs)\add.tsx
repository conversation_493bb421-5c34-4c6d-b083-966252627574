import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { ArrowRight, Chrome as Home, MapPin, Camera, DollarSign, Bed, Bath, Square, Car, Calendar, FileText, CircleCheck as CheckCircle, Plus, X } from 'lucide-react-native';

interface PropertyData {
  // Basic Info
  title: string;
  description: string;
  propertyType: string;
  
  // Location
  address: string;
  city: string;
  state: string;
  
  // Details
  price: string;
  bedrooms: string;
  bathrooms: string;
  area: string;
  yearBuilt: string;
  lotSize: string;
  garage: string;
  
  // Features
  features: string[];
  
  // Images
  images: string[];
  
  // Agent Info
  agentName: string;
  agentPhone: string;
  agentEmail: string;
}

const PROPERTY_TYPES = ['Apartment', 'Duplex', 'Bungalow', 'Mansion', 'Penthouse'];
const COMMON_FEATURES = [
  'Generator', 'Swimming Pool', 'Security', 'Parking Space', 'Boys Quarter',
  'Staff Quarters', 'Garden', 'Balcony', 'Fitted Kitchen', 'Air Conditioning',
  'Gym Access', 'Elevator', 'CCTV', 'Backup Water', 'Solar Power'
];

export default function AddPropertyScreen() {
  const [currentStep, setCurrentStep] = useState(1);
  const [propertyData, setPropertyData] = useState<PropertyData>({
    title: '',
    description: '',
    propertyType: '',
    address: '',
    city: '',
    state: 'Lagos',
    price: '',
    bedrooms: '',
    bathrooms: '',
    area: '',
    yearBuilt: '',
    lotSize: '',
    garage: '',
    features: [],
    images: [],
    agentName: '',
    agentPhone: '',
    agentEmail: '',
  });

  const totalSteps = 5;

  const updatePropertyData = (field: keyof PropertyData, value: any) => {
    setPropertyData(prev => ({ ...prev, [field]: value }));
  };

  const addFeature = (feature: string) => {
    if (!propertyData.features.includes(feature)) {
      updatePropertyData('features', [...propertyData.features, feature]);
    }
  };

  const removeFeature = (feature: string) => {
    updatePropertyData('features', propertyData.features.filter(f => f !== feature));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // Validate required fields
    const requiredFields = ['title', 'propertyType', 'address', 'city', 'price', 'bedrooms', 'bathrooms', 'area'];
    const missingFields = requiredFields.filter(field => !propertyData[field as keyof PropertyData]);
    
    if (missingFields.length > 0) {
      if (Platform.OS !== 'web') {
        Alert.alert('Missing Information', 'Please fill in all required fields.');
      }
      return;
    }

    // Submit property
    if (Platform.OS !== 'web') {
      Alert.alert(
        'Success!', 
        'Your property has been submitted for review. It will be published once approved.',
        [{ text: 'OK', onPress: () => router.push('/(tabs)/') }]
      );
    } else {
      router.push('/(tabs)/');
    }
  };

  const ProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View style={[styles.progressFill, { width: `${(currentStep / totalSteps) * 100}%` }]} />
      </View>
      <Text style={styles.progressText}>Step {currentStep} of {totalSteps}</Text>
    </View>
  );

  const StepHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
    <View style={styles.stepHeader}>
      <Text style={styles.stepTitle}>{title}</Text>
      <Text style={styles.stepSubtitle}>{subtitle}</Text>
    </View>
  );

  const InputField = ({ 
    label, 
    value, 
    onChangeText, 
    placeholder, 
    icon, 
    multiline = false,
    keyboardType = 'default' as any,
    required = false
  }: {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    placeholder: string;
    icon: React.ReactNode;
    multiline?: boolean;
    keyboardType?: any;
    required?: boolean;
  }) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>
        {label} {required && <Text style={styles.required}>*</Text>}
      </Text>
      <View style={styles.inputWrapper}>
        <View style={styles.inputIcon}>{icon}</View>
        <TextInput
          style={[styles.input, multiline && styles.multilineInput]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="#9CA3AF"
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={multiline ? 4 : 1}
        />
      </View>
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContent}>
      <StepHeader 
        title="Basic Information" 
        subtitle="Tell us about your property" 
      />
      
      <InputField
        label="Property Title"
        value={propertyData.title}
        onChangeText={(text) => updatePropertyData('title', text)}
        placeholder="e.g., Modern 3-Bedroom Apartment in Lekki"
        icon={<Home size={20} color="#6B7280" />}
        required
      />

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>
          Property Type <Text style={styles.required}>*</Text>
        </Text>
        <View style={styles.typeGrid}>
          {PROPERTY_TYPES.map(type => (
            <TouchableOpacity
              key={type}
              style={[
                styles.typeCard,
                propertyData.propertyType === type && styles.typeCardSelected
              ]}
              onPress={() => updatePropertyData('propertyType', type)}>
              <Text style={[
                styles.typeText,
                propertyData.propertyType === type && styles.typeTextSelected
              ]}>
                {type}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <InputField
        label="Description"
        value={propertyData.description}
        onChangeText={(text) => updatePropertyData('description', text)}
        placeholder="Describe your property in detail..."
        icon={<FileText size={20} color="#6B7280" />}
        multiline
      />
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContent}>
      <StepHeader 
        title="Location Details" 
        subtitle="Where is your property located?" 
      />
      
      <InputField
        label="Street Address"
        value={propertyData.address}
        onChangeText={(text) => updatePropertyData('address', text)}
        placeholder="e.g., 15 Admiralty Way"
        icon={<MapPin size={20} color="#6B7280" />}
        required
      />

      <View style={styles.row}>
        <View style={styles.flexTwo}>
          <InputField
            label="City"
            value={propertyData.city}
            onChangeText={(text) => updatePropertyData('city', text)}
            placeholder="e.g., Lagos"
            icon={<MapPin size={20} color="#6B7280" />}
            required
          />
        </View>
        <View style={styles.flexOne}>
          <InputField
            label="State"
            value={propertyData.state}
            onChangeText={(text) => updatePropertyData('state', text)}
            placeholder="State"
            icon={<MapPin size={20} color="#6B7280" />}
            required
          />
        </View>
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContent}>
      <StepHeader 
        title="Property Details" 
        subtitle="Provide key specifications" 
      />
      
      <InputField
        label="Price (₦)"
        value={propertyData.price}
        onChangeText={(text) => updatePropertyData('price', text)}
        placeholder="e.g., 85000000"
        icon={<DollarSign size={20} color="#6B7280" />}
        keyboardType="numeric"
        required
      />

      <View style={styles.row}>
        <View style={styles.flexOne}>
          <InputField
            label="Bedrooms"
            value={propertyData.bedrooms}
            onChangeText={(text) => updatePropertyData('bedrooms', text)}
            placeholder="3"
            icon={<Bed size={20} color="#6B7280" />}
            keyboardType="numeric"
            required
          />
        </View>
        <View style={styles.flexOne}>
          <InputField
            label="Bathrooms"
            value={propertyData.bathrooms}
            onChangeText={(text) => updatePropertyData('bathrooms', text)}
            placeholder="2"
            icon={<Bath size={20} color="#6B7280" />}
            keyboardType="numeric"
            required
          />
        </View>
        <View style={styles.flexOne}>
          <InputField
            label="Area (sqm)"
            value={propertyData.area}
            onChangeText={(text) => updatePropertyData('area', text)}
            placeholder="120"
            icon={<Square size={20} color="#6B7280" />}
            keyboardType="numeric"
            required
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={styles.flexOne}>
          <InputField
            label="Year Built"
            value={propertyData.yearBuilt}
            onChangeText={(text) => updatePropertyData('yearBuilt', text)}
            placeholder="2020"
            icon={<Calendar size={20} color="#6B7280" />}
            keyboardType="numeric"
          />
        </View>
        <View style={styles.flexOne}>
          <InputField
            label="Lot Size (sqm)"
            value={propertyData.lotSize}
            onChangeText={(text) => updatePropertyData('lotSize', text)}
            placeholder="600"
            icon={<Square size={20} color="#6B7280" />}
            keyboardType="numeric"
          />
        </View>
        <View style={styles.flexOne}>
          <InputField
            label="Garage Spaces"
            value={propertyData.garage}
            onChangeText={(text) => updatePropertyData('garage', text)}
            placeholder="2"
            icon={<Car size={20} color="#6B7280" />}
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContent}>
      <StepHeader 
        title="Features & Amenities" 
        subtitle="What makes your property special?" 
      />
      
      <Text style={styles.featuresLabel}>Select features that apply:</Text>
      <View style={styles.featuresGrid}>
        {COMMON_FEATURES.map(feature => (
          <TouchableOpacity
            key={feature}
            style={[
              styles.featureChip,
              propertyData.features.includes(feature) && styles.featureChipSelected
            ]}
            onPress={() => 
              propertyData.features.includes(feature) 
                ? removeFeature(feature) 
                : addFeature(feature)
            }>
            <Text style={[
              styles.featureChipText,
              propertyData.features.includes(feature) && styles.featureChipTextSelected
            ]}>
              {feature}
            </Text>
            {propertyData.features.includes(feature) && (
              <CheckCircle size={16} color="#FFFFFF" style={styles.featureCheck} />
            )}
          </TouchableOpacity>
        ))}
      </View>

      {propertyData.features.length > 0 && (
        <View style={styles.selectedFeatures}>
          <Text style={styles.selectedFeaturesTitle}>Selected Features:</Text>
          <View style={styles.selectedFeaturesList}>
            {propertyData.features.map(feature => (
              <View key={feature} style={styles.selectedFeatureItem}>
                <Text style={styles.selectedFeatureText}>{feature}</Text>
                <TouchableOpacity onPress={() => removeFeature(feature)}>
                  <X size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}
    </View>
  );

  const renderStep5 = () => (
    <View style={styles.stepContent}>
      <StepHeader 
        title="Contact Information" 
        subtitle="How can interested buyers reach you?" 
      />
      
      <InputField
        label="Agent/Contact Name"
        value={propertyData.agentName}
        onChangeText={(text) => updatePropertyData('agentName', text)}
        placeholder="Your full name"
        icon={<Home size={20} color="#6B7280" />}
      />

      <InputField
        label="Phone Number"
        value={propertyData.agentPhone}
        onChangeText={(text) => updatePropertyData('agentPhone', text)}
        placeholder="+234 ************"
        icon={<Home size={20} color="#6B7280" />}
        keyboardType="phone-pad"
      />

      <InputField
        label="Email Address"
        value={propertyData.agentEmail}
        onChangeText={(text) => updatePropertyData('agentEmail', text)}
        placeholder="<EMAIL>"
        icon={<Home size={20} color="#6B7280" />}
        keyboardType="email-address"
      />

      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Property Summary</Text>
        <Text style={styles.summaryText}>
          {propertyData.title || 'Untitled Property'} • {propertyData.propertyType}
        </Text>
        <Text style={styles.summaryText}>
          {propertyData.bedrooms} bed • {propertyData.bathrooms} bath • {propertyData.area} sqm
        </Text>
        <Text style={styles.summaryText}>
          {propertyData.address}, {propertyData.city}
        </Text>
        <Text style={styles.summaryPrice}>
          ₦{propertyData.price ? new Intl.NumberFormat('en-NG').format(parseInt(propertyData.price)) : '0'}
        </Text>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      case 4: return renderStep4();
      case 5: return renderStep5();
      default: return renderStep1();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ProgressBar />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      <View style={styles.bottomBar}>
        {currentStep > 1 && (
          <TouchableOpacity style={styles.backButton} onPress={prevStep}>
            <Text style={styles.backButtonText}>Back</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity 
          style={[styles.nextButton, currentStep === 1 && styles.nextButtonFull]} 
          onPress={currentStep === totalSteps ? handleSubmit : nextStep}>
          <Text style={styles.nextButtonText}>
            {currentStep === totalSteps ? 'Submit Property' : 'Continue'}
          </Text>
          {currentStep < totalSteps && <ArrowRight size={20} color="#FFFFFF" />}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#10B981',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  stepContent: {
    padding: 20,
  },
  stepHeader: {
    marginBottom: 32,
    alignItems: 'center',
  },
  stepTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  required: {
    color: '#EF4444',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 16,
    minHeight: 48,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    paddingVertical: 12,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  flexOne: {
    flex: 1,
  },
  flexTwo: {
    flex: 2,
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeCard: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    minWidth: 100,
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  typeCardSelected: {
    borderColor: '#10B981',
    backgroundColor: '#F0FDF4',
  },
  typeText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  typeTextSelected: {
    color: '#10B981',
  },
  featuresLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
  },
  featureChip: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureChipSelected: {
    backgroundColor: '#10B981',
    borderColor: '#10B981',
  },
  featureChipText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  featureChipTextSelected: {
    color: '#FFFFFF',
  },
  featureCheck: {
    marginLeft: 6,
  },
  selectedFeatures: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedFeaturesTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 12,
  },
  selectedFeaturesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedFeatureItem: {
    backgroundColor: '#F0FDF4',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  selectedFeatureText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#10B981',
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginTop: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  summaryPrice: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#10B981',
    marginTop: 8,
  },
  bottomBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  backButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  nextButton: {
    flex: 2,
    backgroundColor: '#10B981',
    paddingVertical: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  nextButtonFull: {
    flex: 1,
  },
  nextButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});