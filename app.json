{"expo": {"name": "Roots & Roofing", "slug": "roots-and-roofing", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.rootsandroofing.mobile", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to take photos of properties and roofing work.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select images for property documentation."}}, "android": {"package": "com.rootsandroofing.mobile", "versionCode": 1, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", ["expo-build-properties", {"ios": {"newArchEnabled": true}, "android": {"newArchEnabled": true}}]], "experiments": {"typedRoutes": true}}}