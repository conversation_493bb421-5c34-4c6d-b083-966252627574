import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { PropertyCard } from '@/components/PropertyCard';
import { Heart, SlidersHorizontal } from 'lucide-react-native';
import { router } from 'expo-router';

interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  imageUrl: string;
  type: string;
  isFavorite: boolean;
  description?: string;
  yearBuilt?: number;
  lotSize?: number;
  garage?: number;
  features?: string[];
  images?: string[];
  agent?: {
    name: string;
    phone: string;
    email: string;
    photo: string;
  };
}

const mockFavoriteProperties: Property[] = [
  {
    id: '2',
    title: 'Luxury Ikoyi Mansion',
    price: 250000000,
    location: 'Ikoyi, Lagos',
    bedrooms: 5,
    bathrooms: 6,
    area: 450,
    imageUrl: 'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Mansion',
    isFavorite: true,
    description: 'Exquisite luxury mansion in prestigious Ikoyi.',
    yearBuilt: 2019,
    lotSize: 1200,
    garage: 4,
    features: ['Master Suite', 'Home Cinema', 'Wine Cellar'],
    images: [
      'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Funmi Adebisi',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
  {
    id: '4',
    title: 'Victoria Island Penthouse',
    price: 180000000,
    location: 'Victoria Island, Lagos',
    bedrooms: 4,
    bathrooms: 4,
    area: 300,
    imageUrl: 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
    type: 'Penthouse',
    isFavorite: true,
    description: 'Spectacular penthouse with panoramic views of Lagos lagoon.',
    yearBuilt: 2021,
    lotSize: 0,
    garage: 3,
    features: ['Ocean Views', 'Private Terrace', 'Concierge Service'],
    images: [
      'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    agent: {
      name: 'Kemi Adesanya',
      phone: '+234 ************',
      email: '<EMAIL>',
      photo: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  },
];

export default function FavoritesScreen() {
  const [favorites, setFavorites] = useState<Property[]>(mockFavoriteProperties);

  const toggleFavorite = (propertyId: string) => {
    setFavorites(prev =>
      prev.map(property =>
        property.id === propertyId
          ? { ...property, isFavorite: !property.isFavorite }
          : property
      ).filter(property => property.isFavorite)
    );
  };

  const handlePropertyPress = (property: Property) => {
    router.push({
      pathname: '/property/[id]',
      params: { 
        id: property.id,
        propertyData: JSON.stringify(property)
      }
    });
  };

  const EmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyIconContainer}>
        <Heart size={48} color="#E5E7EB" />
      </View>
      <Text style={styles.emptyTitle}>No Favorites Yet</Text>
      <Text style={styles.emptyText}>
        Start exploring properties and save your favorites to see them here.
      </Text>
      <TouchableOpacity style={styles.exploreButton}>
        <Text style={styles.exploreButtonText}>Explore Properties</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>Favorites</Text>
          <Text style={styles.subtitle}>
            {favorites.length} saved {favorites.length === 1 ? 'property' : 'properties'}
          </Text>
        </View>
        {favorites.length > 0 && (
          <TouchableOpacity style={styles.filterButton}>
            <SlidersHorizontal size={20} color="#10B981" />
          </TouchableOpacity>
        )}
      </View>

      {favorites.length === 0 ? (
        <EmptyState />
      ) : (
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.propertiesContainer}>
            {favorites.map(property => (
              <PropertyCard
                key={property.id}
                property={property}
                onToggleFavorite={toggleFavorite}
                onPress={() => handlePropertyPress(property)}
              />
            ))}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 16,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  filterButton: {
    padding: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  propertiesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    width: 96,
    height: 96,
    backgroundColor: '#F3F4F6',
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  exploreButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  exploreButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});