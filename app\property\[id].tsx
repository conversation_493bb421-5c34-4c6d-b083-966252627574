import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Image,
  TouchableOpacity,
  Dimensions,
  FlatList,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { 
  ArrowLeft, 
  Heart, 
  Share, 
  Bed, 
  Bath, 
  Square, 
  Calendar,
  Car,
  MapPin,
  Phone,
  Mail,
  MessageCircle
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  imageUrl: string;
  type: string;
  isFavorite: boolean;
  description?: string;
  yearBuilt?: number;
  lotSize?: number;
  garage?: number;
  features?: string[];
  images?: string[];
  agent?: {
    name: string;
    phone: string;
    email: string;
    photo: string;
  };
}

export default function PropertyDetailsScreen() {
  const { id, propertyData } = useLocalSearchParams();
  const property: Property = JSON.parse(propertyData as string);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(property.isFavorite);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatArea = (area: number) => {
    return new Intl.NumberFormat('en-US').format(area);
  };

  const formatLotSize = (size: number) => {
    if (size === 0) return 'N/A';
    return new Intl.NumberFormat('en-US').format(size) + ' sqft';
  };

  const images = property.images || [property.imageUrl];

  const renderImageItem = ({ item, index }: { item: string; index: number }) => (
    <Image source={{ uri: item }} style={styles.carouselImage} />
  );

  const FeatureChip = ({ feature }: { feature: string }) => (
    <View style={styles.featureChip}>
      <Text style={styles.featureChipText}>{feature}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Carousel */}
        <View style={styles.imageSection}>
          <FlatList
            data={images}
            renderItem={renderImageItem}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(event.nativeEvent.contentOffset.x / width);
              setCurrentImageIndex(index);
            }}
          />
          
          {/* Image Indicators */}
          {images.length > 1 && (
            <View style={styles.imageIndicators}>
              {images.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.indicator,
                    index === currentImageIndex && styles.activeIndicator,
                  ]}
                />
              ))}
            </View>
          )}

          {/* Header Overlay */}
          <View style={styles.headerOverlay}>
            <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.actionButton}>
                <Share size={20} color="#FFFFFF" />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => setIsFavorite(!isFavorite)}>
                <Heart
                  size={20}
                  color={isFavorite ? '#EF4444' : '#FFFFFF'}
                  fill={isFavorite ? '#EF4444' : 'transparent'}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Property Type Tag */}
          <View style={styles.typeTagOverlay}>
            <Text style={styles.typeText}>{property.type}</Text>
          </View>
        </View>

        {/* Property Information */}
        <View style={styles.contentSection}>
          {/* Price and Location */}
          <View style={styles.priceSection}>
            <Text style={styles.price}>{formatPrice(property.price)}</Text>
            <View style={styles.locationContainer}>
              <MapPin size={16} color="#6B7280" />
              <Text style={styles.location}>{property.location}</Text>
            </View>
          </View>

          {/* Title */}
          <Text style={styles.title}>{property.title}</Text>

          {/* Key Features */}
          <View style={styles.keyFeatures}>
            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Bed size={20} color="#10B981" />
              </View>
              <Text style={styles.featureValue}>{property.bedrooms}</Text>
              <Text style={styles.featureLabel}>Bedrooms</Text>
            </View>
            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Bath size={20} color="#10B981" />
              </View>
              <Text style={styles.featureValue}>{property.bathrooms}</Text>
              <Text style={styles.featureLabel}>Bathrooms</Text>
            </View>
            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Square size={20} color="#10B981" />
              </View>
              <Text style={styles.featureValue}>{formatArea(property.area)}</Text>
              <Text style={styles.featureLabel}>Sq Ft</Text>
            </View>
            {property.garage && property.garage > 0 && (
              <View style={styles.feature}>
                <View style={styles.featureIcon}>
                  <Car size={20} color="#10B981" />
                </View>
                <Text style={styles.featureValue}>{property.garage}</Text>
                <Text style={styles.featureLabel}>Garage</Text>
              </View>
            )}
          </View>

          {/* Description */}
          {property.description && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.description}>{property.description}</Text>
            </View>
          )}

          {/* Property Details */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Property Details</Text>
            <View style={styles.detailsGrid}>
              {property.yearBuilt && (
                <View style={styles.detailItem}>
                  <Calendar size={16} color="#6B7280" />
                  <Text style={styles.detailLabel}>Year Built</Text>
                  <Text style={styles.detailValue}>{property.yearBuilt}</Text>
                </View>
              )}
              {property.lotSize !== undefined && (
                <View style={styles.detailItem}>
                  <Square size={16} color="#6B7280" />
                  <Text style={styles.detailLabel}>Lot Size</Text>
                  <Text style={styles.detailValue}>{formatLotSize(property.lotSize)}</Text>
                </View>
              )}
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Property Type</Text>
                <Text style={styles.detailValue}>{property.type}</Text>
              </View>
            </View>
          </View>

          {/* Features */}
          {property.features && property.features.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Features & Amenities</Text>
              <View style={styles.featuresContainer}>
                {property.features.map((feature, index) => (
                  <FeatureChip key={index} feature={feature} />
                ))}
              </View>
            </View>
          )}

          {/* Agent Information */}
          {property.agent && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Listed By</Text>
              <View style={styles.agentCard}>
                <Image source={{ uri: property.agent.photo }} style={styles.agentPhoto} />
                <View style={styles.agentInfo}>
                  <Text style={styles.agentName}>{property.agent.name}</Text>
                  <Text style={styles.agentTitle}>Real Estate Agent</Text>
                  <View style={styles.agentContact}>
                    <TouchableOpacity style={styles.contactButton}>
                      <Phone size={16} color="#10B981" />
                      <Text style={styles.contactText}>Call</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.contactButton}>
                      <Mail size={16} color="#10B981" />
                      <Text style={styles.contactText}>Email</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.contactButton}>
                      <MessageCircle size={16} color="#10B981" />
                      <Text style={styles.contactText}>Message</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Action Bar */}
      <View style={styles.bottomBar}>
        <TouchableOpacity style={styles.scheduleButton}>
          <Text style={styles.scheduleButtonText}>Schedule Tour</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.inquireButton}>
          <Text style={styles.inquireButtonText}>Make Offer</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  imageSection: {
    position: 'relative',
    height: 300,
  },
  carouselImage: {
    width: width,
    height: 300,
    resizeMode: 'cover',
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    background: 'linear-gradient(180deg, rgba(0,0,0,0.3) 0%, transparent 100%)',
  },
  backButton: {
    width: 40,
    height: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    width: 40,
    height: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typeTagOverlay: {
    position: 'absolute',
    bottom: 16,
    left: 20,
    backgroundColor: 'rgba(16, 185, 129, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  typeText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  contentSection: {
    padding: 20,
  },
  priceSection: {
    marginBottom: 16,
  },
  price: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  location: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 24,
    lineHeight: 32,
  },
  keyFeatures: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  feature: {
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 48,
    height: 48,
    backgroundColor: '#F0FDF4',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  featureValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  featureLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  detailsGrid: {
    gap: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  detailLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    flex: 1,
    marginLeft: 12,
  },
  detailValue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  featureChip: {
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#BBF7D0',
  },
  featureChipText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#059669',
  },
  agentCard: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  agentPhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  agentTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 12,
  },
  agentContact: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  contactText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#10B981',
    marginLeft: 4,
  },
  bottomBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  scheduleButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  scheduleButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  inquireButton: {
    flex: 1,
    backgroundColor: '#10B981',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  inquireButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});