import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { 
  ArrowLeft, 
  Camera, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Save,
  Eye,
  EyeOff
} from 'lucide-react-native';

interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  dateOfBirth: string;
  bio: string;
  avatar: string;
}

export default function EditProfileScreen() {
  const [profile, setProfile] = useState<UserProfile>({
    firstName: 'Adebayo',
    lastName: 'Ogundimu',
    email: '<EMAIL>',
    phone: '+234 ************',
    address: '15 Admiralty Way',
    city: 'Lagos',
    state: 'Lagos',
    zipCode: '101001',
    dateOfBirth: '1985-03-20',
    bio: 'Real estate enthusiast looking for the perfect home in Lagos and Abuja.',
    avatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=200'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<UserProfile>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<UserProfile> = {};

    if (!profile.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!profile.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!profile.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(profile.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!profile.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!profile.zipCode.trim()) {
      newErrors.zipCode = 'Postal code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Show success message
      if (Platform.OS !== 'web') {
        Alert.alert('Success', 'Profile updated successfully!');
      }
      
      router.back();
    } catch (error) {
      if (Platform.OS !== 'web') {
        Alert.alert('Error', 'Failed to update profile. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhotoPress = () => {
    if (Platform.OS !== 'web') {
      Alert.alert(
        'Change Photo',
        'Choose an option',
        [
          { text: 'Camera', onPress: () => console.log('Camera selected') },
          { text: 'Photo Library', onPress: () => console.log('Library selected') },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    }
  };

  const updateProfile = (field: keyof UserProfile, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const InputField = ({ 
    label, 
    value, 
    onChangeText, 
    placeholder, 
    icon, 
    error,
    multiline = false,
    keyboardType = 'default' as any,
    autoCapitalize = 'words' as any
  }: {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    placeholder: string;
    icon: React.ReactNode;
    error?: string;
    multiline?: boolean;
    keyboardType?: any;
    autoCapitalize?: any;
  }) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <View style={[styles.inputWrapper, error && styles.inputError]}>
        <View style={styles.inputIcon}>{icon}</View>
        <TextInput
          style={[styles.input, multiline && styles.multilineInput]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="#9CA3AF"
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          multiline={multiline}
          numberOfLines={multiline ? 4 : 1}
        />
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
        <TouchableOpacity 
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
          onPress={handleSave}
          disabled={isLoading}>
          <Save size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Photo Section */}
        <View style={styles.photoSection}>
          <TouchableOpacity style={styles.photoContainer} onPress={handlePhotoPress}>
            <Image source={{ uri: profile.avatar }} style={styles.profilePhoto} />
            <View style={styles.photoOverlay}>
              <Camera size={24} color="#FFFFFF" />
            </View>
          </TouchableOpacity>
          <Text style={styles.photoLabel}>Tap to change photo</Text>
        </View>

        {/* Form Fields */}
        <View style={styles.formSection}>
          {/* Personal Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Personal Information</Text>
            
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <InputField
                  label="First Name"
                  value={profile.firstName}
                  onChangeText={(text) => updateProfile('firstName', text)}
                  placeholder="Enter first name"
                  icon={<User size={20} color="#6B7280" />}
                  error={errors.firstName}
                />
              </View>
              <View style={styles.halfWidth}>
                <InputField
                  label="Last Name"
                  value={profile.lastName}
                  onChangeText={(text) => updateProfile('lastName', text)}
                  placeholder="Enter last name"
                  icon={<User size={20} color="#6B7280" />}
                  error={errors.lastName}
                />
              </View>
            </View>

            <InputField
              label="Email Address"
              value={profile.email}
              onChangeText={(text) => updateProfile('email', text)}
              placeholder="Enter email address"
              icon={<Mail size={20} color="#6B7280" />}
              error={errors.email}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <InputField
              label="Phone Number"
              value={profile.phone}
              onChangeText={(text) => updateProfile('phone', text)}
              placeholder="Enter phone number"
              icon={<Phone size={20} color="#6B7280" />}
              error={errors.phone}
              keyboardType="phone-pad"
            />

            <InputField
              label="Date of Birth"
              value={profile.dateOfBirth}
              onChangeText={(text) => updateProfile('dateOfBirth', text)}
              placeholder="YYYY-MM-DD"
              icon={<Calendar size={20} color="#6B7280" />}
              keyboardType="numeric"
            />
          </View>

          {/* Address Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Address</Text>
            
            <InputField
              label="Street Address"
              value={profile.address}
              onChangeText={(text) => updateProfile('address', text)}
              placeholder="Enter street address"
              icon={<MapPin size={20} color="#6B7280" />}
            />

            <View style={styles.row}>
              <View style={styles.flexTwo}>
                <InputField
                  label="City"
                  value={profile.city}
                  onChangeText={(text) => updateProfile('city', text)}
                  placeholder="Enter city"
                  icon={<MapPin size={20} color="#6B7280" />}
                />
              </View>
              <View style={styles.flexOne}>
                <InputField
                  label="State"
                  value={profile.state}
                  onChangeText={(text) => updateProfile('state', text)}
                  placeholder="State"
                  icon={<MapPin size={20} color="#6B7280" />}
                />
              </View>
              <View style={styles.flexOne}>
                <InputField
                  label="Postal Code"
                  value={profile.zipCode}
                  onChangeText={(text) => updateProfile('zipCode', text)}
                  placeholder="Postal Code"
                  icon={<MapPin size={20} color="#6B7280" />}
                  error={errors.zipCode}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>

          {/* Bio Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About</Text>
            <InputField
              label="Bio"
              value={profile.bio}
              onChangeText={(text) => updateProfile('bio', text)}
              placeholder="Tell us about yourself..."
              icon={<User size={20} color="#6B7280" />}
              multiline={true}
            />
          </View>
        </View>

        {/* Save Button */}
        <View style={styles.buttonSection}>
          <TouchableOpacity 
            style={[styles.saveButtonLarge, isLoading && styles.saveButtonDisabled]} 
            onPress={handleSave}
            disabled={isLoading}>
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  saveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  scrollView: {
    flex: 1,
  },
  photoSection: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: '#FFFFFF',
    marginBottom: 16,
  },
  photoContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  profilePhoto: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  photoOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 36,
    height: 36,
    backgroundColor: '#10B981',
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  photoLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  formSection: {
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  flexOne: {
    flex: 1,
  },
  flexTwo: {
    flex: 2,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 16,
    minHeight: 48,
  },
  inputError: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    paddingVertical: 12,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
    marginTop: 4,
    marginLeft: 4,
  },
  buttonSection: {
    paddingHorizontal: 20,
    paddingBottom: 32,
  },
  saveButtonLarge: {
    backgroundColor: '#10B981',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#10B981',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  saveButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});