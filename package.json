{"name": "roots-and-roofing", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "build:ios:dev": "eas build --platform ios --profile development", "build:ios:prod": "eas build --platform ios --profile production", "build:android:dev": "eas build --platform android --profile development", "build:android:prod": "eas build --platform android --profile production", "build:preview": "eas build --platform all --profile preview", "build:all": "eas build --platform all --profile production", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android", "lint": "expo lint", "// Development build scripts": "Commands for generating installable files for testing", "build:android:apk": "eas build --platform android --profile development --local", "build:ios:ipa": "eas build --platform ios --profile development --local", "build:dev:all": "npm run build:android:apk && npm run build:ios:ipa", "build:local:all": "eas build --platform all --profile development --local"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@expo-google-fonts/inter": "^0.2.3", "@lucide/lab": "^0.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "^53.0.0", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~5.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}