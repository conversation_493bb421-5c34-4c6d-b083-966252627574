import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Heart, Bed, Bath, Square } from 'lucide-react-native';

const { width } = Dimensions.get('window');

interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  imageUrl: string;
  type: string;
  isFavorite: boolean;
}

interface PropertyCardProps {
  property: Property;
  onToggleFavorite: (id: string) => void;
  onPress?: () => void;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  onToggleFavorite,
  onPress,
}) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatArea = (area: number) => {
    return new Intl.NumberFormat('en-NG').format(area);
  };

  const handleFavoritePress = (e: any) => {
    e.stopPropagation();
    onToggleFavorite(property.id);
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.imageContainer}>
        <Image source={{ uri: property.imageUrl }} style={styles.image} />
        <View style={styles.typeTag}>
          <Text style={styles.typeText}>{property.type}</Text>
        </View>
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={handleFavoritePress}>
          <Heart
            size={20}
            color={property.isFavorite ? '#EF4444' : '#FFFFFF'}
            fill={property.isFavorite ? '#EF4444' : 'transparent'}
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.price}>{formatPrice(property.price)}</Text>
          <Text style={styles.location}>{property.location}</Text>
        </View>
        
        <Text style={styles.title}>{property.title}</Text>
        
        <View style={styles.features}>
          <View style={styles.feature}>
            <Bed size={16} color="#6B7280" />
            <Text style={styles.featureText}>{property.bedrooms} bed</Text>
          </View>
          <View style={styles.feature}>
            <Bath size={16} color="#6B7280" />
            <Text style={styles.featureText}>{property.bathrooms} bath</Text>
          </View>
          <View style={styles.feature}>
            <Square size={16} color="#6B7280" />
            <Text style={styles.featureText}>{formatArea(property.area)} sqm</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  typeTag: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(16, 185, 129, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  typeText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  favoriteButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 36,
    height: 36,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    padding: 20,
  },
  header: {
    marginBottom: 8,
  },
  price: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 16,
    lineHeight: 24,
  },
  features: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
});